// MSSQL存储过程：
-- =============================================
-- Author:		<Author,,Name>
-- Create date: <Create Date,,>
-- Description:	<Description,,>
-- drop proc [fx_cjlfx_drl]
-- fx_cjl_drl
-- =============================================
ALTER PROCEDURE [dbo].[fx_cjl_drl]
-- fx_cjl_drl '1ba44013-8c9f-42c7-b980-2d460ceceb2a','380','439577','001','300.0000','IU','01'
---单次超极量分析
@code nvarchar(50),
@akb020  nvarchar(20),
@yp_code nvarchar(20),
@yp_tj    nvarchar(20),
@dcsl    nvarchar(20),
@gydw     nvarchar(20),
@gypc    nvarchar(20)
AS

declare @sda_id nvarchar(20)
declare @sda_tj nvarchar(20)
declare @sda_drcs nvarchar(20)
declare @sda_dcyl nvarchar(20)
declare @sda_dryl nvarchar(20)
declare @yl_unit nvarchar(20)
declare @yl_unit1 nvarchar(20)

BEGIN


return
declare @ywa_name nvarchar(50)
select @ywa_name=DRUG_NAME from ITF_HOS_DRUG where DRUG_CODE =@yp_code

select @sda_id=sda_id from t_byyydzb  where akb020 =@akb020 and yp_code=@yp_code;
select @sda_tj=by_code from t_tjdzb where h_tj =@yp_tj and akb020 =@akb020 ;
--select @sda_dcyl=minjl*cast(@dcsl as decimal) from T_HIS_DRUG where ypcode = @yp_code and unit=@gydw;
select @sda_drcs=dbo.is_0(DAILY_TIMES) from ITF_HOS_FREQUENCY where freq_code =@gypc ;
set @sda_dryl =cast(@dcsl as decimal(14,3))*cast(@sda_drcs as decimal(14,3))
select distinct @yl_unit=yl_unit from t_sda_max where sda_id =@sda_id

select distinct @yl_unit1=UnitRem from t_sda where id =@sda_id


if @gydw='g'
set @gydw ='克'
if  @gydw='mg'
set @gydw ='毫克'
if @gydw='ml'
set @gydw ='毫升'
if @gydw <>@yl_unit1
begin
return
end


if @yl_unit is null or @yl_unit =''
----单次超极量分析
select @code, @ywa_name ywa,'' ywb,'1'  wtlvlcode,
  '重要警示'  wtlvl,
  'RLT010'  wtcode,
  'CJLJJ_DRL'  wtsp,
  '超极量' wtname,
 '【'+ CAST(c.tymc as varchar)+'】' +'单日用量超最大日极量'  title,
  '说明书提示：【'+ CAST(c.tymc as varchar)+'】 单日最大极量为：' +cast(a.maxcount as varchar)+@yl_unit1 as detail,0,'超极量单日'
from t_sda c left join t_sda_max a on  a.sda_id =c.ID  and a.jlbs ='1' where c.ID =@sda_id and a.gytj_code=@sda_tj
and cast(a.maxcount as float)  <@sda_dryl

if @yl_unit is not null

select @code, @ywa_name ywa,'' ywb,'1'  wtlvlcode,
  '重要警示'  wtlvl,
  'RLT010'  wtcode,
  'CJLJJ_DRL'  wtsp,
  '超极量' wtname,
 '【'+ CAST(c.tymc as varchar)+'】' +'单日用量超最大日极量'  title,
  '说明书提示：【'+ CAST(c.tymc as varchar)+'】 单日最大极量为：' +cast(a.maxcount as varchar)+@yl_unit1 as detail,0,'超极量单日'
from t_sda c left join t_sda_max a on  a.sda_id =c.ID  and a.jlbs ='1' where c.ID =@sda_id and a.gytj_code=@sda_tj
and cast(a.maxcount as float)  <@sda_dryl and a.yl_unit =@gydw





END








// MySQL存储过程：
CREATE DEFINER=`root`@`%` PROCEDURE `rms_fx_cjl_drl`(IN p_code VARCHAR(50),
    IN p_akb020 VARCHAR(20),
    IN p_yp_code VARCHAR(20),
    IN p_yp_tj VARCHAR(20),
    IN p_dcsl VARCHAR(20),
    IN p_gydw VARCHAR(20),
    IN p_gypc VARCHAR(20)
)
    COMMENT '单日超极量分析存储过程'
main_block: BEGIN

		DECLARE v_sda_id VARCHAR(20);
		DECLARE v_sda_tj VARCHAR(20);
		DECLARE v_sda_drcs VARCHAR(20);
		DECLARE v_sda_dcyl VARCHAR(20);
		DECLARE v_sda_dryl DECIMAL(14,3);
		DECLARE v_yl_unit VARCHAR(20);
		DECLARE v_yl_unit1 VARCHAR(20);
		DECLARE v_ywa_name VARCHAR(50);
		DECLARE v_gydw_converted VARCHAR(20);

		-- 异常处理
		DECLARE CONTINUE HANDLER FOR SQLEXCEPTION
		BEGIN
				GET DIAGNOSTICS CONDITION 1
            @sqlstate = RETURNED_SQLSTATE,
            @errno = MYSQL_ERRNO,
            @text = MESSAGE_TEXT;

				select CONCAT('执行异常: ', @sqlstate, @errno, @text);
		END;

		-- 获取药品名称
		SELECT DRUG_NAME INTO v_ywa_name
		FROM rms_ITF_HOS_DRUG
		WHERE DRUG_CODE = p_yp_code LIMIT 1;

		-- 获取标准药品ID
		SELECT sda_id INTO v_sda_id
		FROM rms_t_byyydzb
		WHERE akb020 = p_akb020 AND yp_code = p_yp_code LIMIT 1;

		-- 获取剂型编码
		SELECT by_code INTO v_sda_tj
		FROM rms_t_tjdzb
		WHERE h_tj = p_yp_tj AND akb020 = p_akb020 LIMIT 1;

		-- 获取每日次数
		SELECT IFNULL(DAILY_TIMES, '0') INTO v_sda_drcs
		FROM rms_ITF_HOS_FREQUENCY
		WHERE freq_code = p_gypc LIMIT 1;

		-- 计算单日用量
		SET v_sda_dryl = CAST(p_dcsl AS DECIMAL(14,3)) * CAST(v_sda_drcs AS DECIMAL(14,3));

		-- 获取用量单位
		SELECT DISTINCT yl_unit INTO v_yl_unit
		FROM rms_t_sda_max
		WHERE sda_id = v_sda_id LIMIT 1;

		SELECT DISTINCT UnitRem INTO v_yl_unit1
		FROM rms_t_sda
		WHERE id = v_sda_id LIMIT 1;

		-- 单位转换
		SET v_gydw_converted = p_gydw;
		IF p_gydw = 'g' THEN
				SET v_gydw_converted = '克';
		ELSEIF p_gydw = 'mg' THEN
				SET v_gydw_converted = '毫克';
		ELSEIF p_gydw = 'ml' THEN
				SET v_gydw_converted = '毫升';
		END IF;

		-- 单位不匹配则退出
		IF v_gydw_converted != v_yl_unit1 THEN
				LEAVE main_block;
		END IF;

		-- 如果没有特定单位限制
		IF v_yl_unit IS NULL OR v_yl_unit = '' THEN
				INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				SELECT
						p_code,
						v_ywa_name AS ywa,
						'' AS ywb,
						'1' AS wtlvlcode,
						'重要警示' AS wtlvl,
						'RLT010' AS wtcode,
						'CJLJJ_DRL' AS wtsp,
						'超极量' AS wtname,
						CONCAT('【', CAST(c.tymc AS CHAR), '】', '单日用量超最大日极量') AS title,
						CONCAT('说明书提示：【', CAST(c.tymc AS CHAR), '】 单日最大极量为：', CAST(a.maxcount AS CHAR), v_yl_unit1) AS detail,
						0,
						'超极量单日'
				FROM rms_t_sda c
				LEFT JOIN rms_t_sda_max a ON a.sda_id = c.ID AND a.jlbs = '1'
				WHERE c.ID = v_sda_id
						AND a.gytj_code = v_sda_tj
						AND CAST(a.maxcount AS DECIMAL(14,4)) < v_sda_dryl;
		ELSE
				-- 有特定单位限制
				INSERT INTO rms_t_pres_fx (
					Code, ywa, ywb, wtlvlcode, wtlvl, wtcode, wtsp, wtname, title, detail, flag, text
				)
				SELECT
						p_code,
						v_ywa_name AS ywa,
						'' AS ywb,
						'1' AS wtlvlcode,
						'重要警示' AS wtlvl,
						'RLT010' AS wtcode,
						'CJLJJ_DRL' AS wtsp,
						'超极量' AS wtname,
						CONCAT('【', CAST(c.tymc AS CHAR), '】', '单日用量超最大日极量') AS title,
						CONCAT('说明书提示：【', CAST(c.tymc AS CHAR), '】 单日最大极量为：', CAST(a.maxcount AS CHAR), v_yl_unit1) AS detail,
						0,
						'超极量单日'
				FROM rms_t_sda c
				LEFT JOIN rms_t_sda_max a ON a.sda_id = c.ID AND a.jlbs = '1'
				WHERE c.ID = v_sda_id
						AND a.gytj_code = v_sda_tj
						AND CAST(a.maxcount AS DECIMAL(14,4)) < v_sda_dryl
						AND a.yl_unit = p_gydw;
		END IF;

END
